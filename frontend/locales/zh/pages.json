{"home": {"title": "为 AI 编程助手提供最新文档", "description": "索引和查询 GitHub/GitLab 仓库，与 Cursor、Windsurf 等工具通过 MCP 集成。消除代码幻觉，让 AI 编写更可靠的代码。", "searchPlaceholder": "搜索仓库...", "addRepository": "添加 Git 仓库", "stats": {"indexedRepositories": "已索引仓库", "totalTokens": "总 Token 数", "totalSnippets": "总代码片段数"}, "loading": "正在加载仓库数据...", "error": "加载仓库数据失败", "noRepositories": "未找到仓库。", "tryAgain": "重试", "table": {"name": "名称", "repository": "仓库", "tokens": "Token 数", "snippets": "代码片段数", "lastUpdated": "最近更新时间", "status": "状态", "actions": "操作"}}, "query": {"title": "文档查询", "description": "在已索引的仓库中搜索相关文档", "placeholder": "输入您的问题...", "noResults": "未找到相关结果", "searchButton": "搜索", "queryPlaceholder": "在此仓库中搜索文档...", "searchResults": "搜索结果", "summary": "智能摘要", "copyContent": "复制内容", "viewOnGitHub": "在 GitHub 上查看", "lastUpdated": "最近更新时间", "tokens": "Token 数", "snippets": "代码片段数", "queryButton": "查询", "querying": "查询中...", "queryingDocuments": "正在查询文档，请稍候...", "queryResults": "查询结果", "backToHome": "返回首页"}, "download": {"title": "添加新仓库", "description": "输入 GitHub/GitLab 仓库 URL，系统将自动下载并索引文档", "urlLabel": "仓库 URL", "urlPlaceholder": "输入 GitHub/GitLab 仓库 URL", "libraryNameLabel": "库名称", "libraryNamePlaceholder": "输入库的显示名称", "platformLabel": "选择 Git 平台", "downloadButton": "下载并索引", "usageInstructions": "使用说明", "step1": "输入完整的 GitHub 仓库 URL", "step2": "点击\"下载并索引\"按钮", "step3": "系统将在后台自动完成 Markdown 文件的下载和索引工作，您可随时关闭当前页面继续其他操作", "step4": "索引完成后，您可以对该仓库进行查询", "processingStarted": "仓库处理已在后台开始。您可以在仓库处理过程中继续使用应用程序。", "downloadProgress": "下载进度", "downloadedFiles": "已下载 {{count}} 个 Markdown 文件", "downloadComplete": "下载完成", "embeddingProgress": "嵌入进度", "embeddedDocuments": "成功将 {{count}} 个文档嵌入到表 {{table}}", "embeddingComplete": "嵌入完成", "preparingDownload": "准备下载...", "waitingForEmbedding": "等待嵌入开始...", "downloadError": "下载错误", "embeddingError": "嵌入错误"}, "settings": {"title": "设置", "description": "配置平台和 MCP 设置", "platformConfigs": "平台配置", "mcpSettings": "MCP 设置", "profile": "个人资料", "account": "账户设置", "preferences": "偏好设置", "language": "语言", "theme": "主题", "notifications": "通知", "privacy": "隐私", "security": "安全"}, "mcp": {"title": "MCP 服务器", "description": "通过模型上下文协议在 AI 工具中访问您的文档库。", "online": "在线", "yourMcpUrl": "您的 MCP URL", "personalUrl": "这是您的个人 MCP 服务器 URL，可在兼容 MCP 的 AI 工具中使用。", "connectionSuccess": "✓ 连接测试成功", "connectionFailed": "✗ 连接测试失败", "configExample": "集成配置示例", "signInRequired": "请登录以获取您的 MCP URL", "installation": "安装", "clickToCopy": "点击复制配置", "availableTools": "可用工具", "searchLibraryId": "搜索库 ID", "searchLibraryDesc": "在您可访问的仓库中按名称搜索库 ID", "getLibraryDocs": "获取库文档", "getLibraryDocsDesc": "获取用户可访问的特定库的文档", "parameters": "参数"}, "profile": {"title": "个人资料", "description": "管理其他人在平台上看到的您的信息", "basicInfo": "基本信息", "changeAvatar": "更换头像", "dragDropImage": "或将图片拖放到页面上的任何位置", "displayName": "显示名称", "username": "用户名", "useCustomUsername": "使用自定义用户名", "bio": "个人简介", "bioPlaceholder": "介绍一下您自己", "charactersRemaining": "剩余字符", "socialLinks": "社交链接", "website": "网站", "websitePlaceholder": "yourwebsite.com", "githubUrl": "GitHub URL", "githubPlaceholder": "github.com/username", "twitterUrl": "Twitter URL", "twitterPlaceholder": "twitter.com/username", "saveChanges": "保存更改"}, "gitCredentials": {"addTitle": "添加 Git 凭证", "addDescription": "添加和管理多个 GitHub 和 GitLab 凭证。您可以为不同的 Git 实例配置不同的凭证和 URL。", "addGithub": "添加 GitHub", "addGitlab": "添加 GitLab", "name": "名称", "namePlaceholder": "输入描述性名称", "baseUrl": "Base URL", "baseUrlPlaceholder": "输入 Base URL", "accessToken": "访问凭证", "tokenPlaceholder": "输入您的访问凭证", "default": "默认", "saving": "保存中...", "saveConfigurations": "保存配置", "savedTitle": "已保存的 Git 凭证", "savedDescription": "管理您现有的 Git 凭证", "platform": "平台", "token": "凭证", "actions": "操作", "noConfigurations": "尚未保存任何配置。点击\"添加 GitHub\"或\"添加 GitLab\"开始。", "backToHomepage": "返回首页", "deleteTitle": "删除 Git 凭证？", "deleteDescription": "您确定要删除凭证", "deleteWarning": "此操作无法撤销。删除后凭证将不再可用。", "cancel": "取消", "delete": "删除", "editTitle": "编辑配置", "saveChanges": "保存更改", "cancelEdit": "取消编辑", "duplicateName": "配置名称重复", "duplicateNameDesc": "名称已被使用。请选择不同的名称。", "duplicateUrl": "Base URL 重复", "duplicateUrlDesc": "Base URL 已配置。请使用不同的 URL。", "validationError": "验证错误", "fillAllFields": "请填写所有必填字段", "configNotFound": "未找到配置", "success": "成功", "configSaved": "平台配置保存成功", "configDeleted": "配置删除成功", "configUpdated": "配置更新成功", "error": "错误", "saveFailed": "保存配置失败", "deleteFailed": "删除配置失败", "updateFailed": "更新配置失败", "loadFailed": "加载平台配置失败"}}