[project]
name = "doc2dev"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiofiles==24.1.0",
    "authlib==1.3.2",
    "bcrypt==4.2.1",
    "dashscope==1.23.2",
    "fastapi==0.115.12",
    "fastmcp==2.2.5",
    "httpx==0.28.1",
    "langchain-anthropic>=0.3.12",
    "langchain-community==0.3.21",
    "langchain-core==0.3.56",
    "langchain-huggingface>=0.1.2",
    "langchain-oceanbase==0.2.0",
    "langchain-openai==0.3.14",
    "langchain-text-splitters==0.3.8",
    "markdown>=3.8.2",
    "markdown-it-py>=3.0.0",
    "pydantic==2.11.3",
    "pygithub==2.6.1",
    "pyjwt==2.10.1",
    "python-dotenv==1.1.0",
    "python-gitlab>=6.2.0",
    "python-multipart==0.0.20",
    "sqlalchemy>=1.4,<2.0.36",
    "unstructured>=0.18.9",
    "uvicorn==0.34.2",
    "torch==2.2.0"
]
