{"home": {"title": "Up-to-date docs for AI code assistants", "description": "Index and query GitHub/GitLab repositories, integrating with tools like Cursor, Windsurf via MCP. Eliminate code hallucinations and make AI write more reliable code.", "searchPlaceholder": "Search repositories...", "addRepository": "Add Repository", "stats": {"indexedRepositories": "Indexed Repositories", "totalTokens": "Total Token Count", "totalSnippets": "Total Snippet Count"}, "loading": "Loading repository data...", "error": "Failed to load repository data", "noRepositories": "No repositories found.", "tryAgain": "Try Again", "table": {"name": "Name", "repository": "Repository", "tokens": "Token Count", "snippets": "Snippet Count", "lastUpdated": "Last Updated", "status": "Status", "actions": "Actions"}}, "query": {"title": "Documentation Query", "description": "Search for relevant documentation in indexed repositories", "placeholder": "Enter your question...", "noResults": "No relevant results found", "searchButton": "Search", "queryPlaceholder": "Search documentation in this repository...", "searchResults": "Search Results", "summary": "AI Summary", "copyContent": "Copy Content", "viewOnGitHub": "View on GitHub", "lastUpdated": "Last Updated", "tokens": "Token Count", "snippets": "Snippet Count", "queryButton": "Query", "querying": "Querying...", "queryingDocuments": "Querying documents, please wait...", "queryResults": "Query Results", "backToHome": "Back to Homepage"}, "download": {"title": "Add New Repository", "description": "Enter GitHub/GitLab repository URL, the system will automatically download and index the documentation", "urlLabel": "Repository URL", "urlPlaceholder": "Enter GitHub/GitLab repository URL", "libraryNameLabel": "Library Name", "libraryNamePlaceholder": "Enter display name for the library", "platformLabel": "Select Git Platform", "downloadButton": "Download & Index", "usageInstructions": "Usage Instructions", "step1": "Enter the complete GitHub repository URL", "step2": "Click the \"Download & Index\" button", "step3": "The system will automatically download and index the Markdown files in the background. You can safely close this page and proceed with other tasks", "step4": "Once indexing is complete, you can perform searches on this repository", "processingStarted": "Repository processing started in background. You can continue using the application while the repository is being processed.", "downloadProgress": "Download Progress", "downloadedFiles": "Downloaded {{count}} Markdown files", "downloadComplete": "Download Complete", "embeddingProgress": "Embedding Progress", "embeddedDocuments": "Successfully embedded {{count}} documents to table {{table}}", "embeddingComplete": "Embedding Complete", "preparingDownload": "Preparing download...", "waitingForEmbedding": "Waiting for embedding to start...", "downloadError": "Download Error", "embeddingError": "Embedding Error"}, "settings": {"title": "Settings", "description": "Configure platform and MCP settings", "platformConfigs": "Platform Configurations", "mcpSettings": "MCP Settings", "profile": "Profile", "account": "Account <PERSON><PERSON>", "preferences": "Preferences", "language": "Language", "theme": "Theme", "notifications": "Notifications", "privacy": "Privacy", "security": "Security"}, "mcp": {"title": "MCP Server", "description": "Access your document libraries in AI tools through Model Context Protocol.", "online": "Online", "yourMcpUrl": "Your MCP URL", "personalUrl": "This is your personal MCP server URL that can be used in MCP-compatible AI tools.", "connectionSuccess": "✓ Connection test successful", "connectionFailed": "✗ Connection test failed", "configExample": "Integration Configuration Example", "signInRequired": "Please sign in to get your MCP URL", "installation": "Installation", "clickToCopy": "Click to copy config", "availableTools": "Available Tools", "searchLibraryId": "Search library IDs", "searchLibraryDesc": "Search for library IDs by name within your accessible repositories", "getLibraryDocs": "Get library documentation", "getLibraryDocsDesc": "Get documentation for a specific library accessible to the user", "parameters": "Parameters"}, "profile": {"title": "Profile", "description": "Manage how others see you on the platform", "basicInfo": "Basic information", "changeAvatar": "Change Avatar", "dragDropImage": "Or drag and drop an image anywhere on the page", "displayName": "Display Name", "username": "Username", "useCustomUsername": "Use custom username", "bio": "Bio", "bioPlaceholder": "Tell us about yourself", "charactersRemaining": "characters remaining", "socialLinks": "Social links", "website": "Website", "websitePlaceholder": "yourwebsite.com", "githubUrl": "GitHub URL", "githubPlaceholder": "github.com/username", "twitterUrl": "Twitter URL", "twitterPlaceholder": "twitter.com/username", "saveChanges": "Save Changes"}, "gitCredentials": {"addTitle": "Add Git Credentials", "addDescription": "Add and manage multiple GitHub and GitLab credentials. You can configure different tokens and URLs for various Git instances.", "addGithub": "Add GitHub", "addGitlab": "Add GitLab", "name": "Name", "namePlaceholder": "Enter a descriptive name", "baseUrl": "Base URL", "baseUrlPlaceholder": "Enter the base URL", "accessToken": "Access Token", "tokenPlaceholder": "Enter your access token", "default": "<PERSON><PERSON><PERSON>", "saving": "Saving...", "saveConfigurations": "Save Configurations", "savedTitle": "Saved Git Credentials", "savedDescription": "Manage your existing Git Credentials", "platform": "Platform", "token": "Token", "actions": "Actions", "noConfigurations": "No configurations saved yet. Click \"Add GitHub\" or \"Add GitLab\" to get started.", "backToHomepage": "Back to Homepage", "deleteTitle": "Delete Git Credentials?", "deleteDescription": "Are you sure you want to delete credentials", "deleteWarning": "This action cannot be undone. The credentials will no longer be usable after deletion.", "cancel": "Cancel", "delete": "Delete", "editTitle": "Edit configuration", "saveChanges": "Save changes", "cancelEdit": "Cancel editing", "duplicateName": "Duplicate Configuration Name", "duplicateNameDesc": "is already used. Please choose a different name.", "duplicateUrl": "Duplicate Base URL", "duplicateUrlDesc": "is already configured. Please use a different URL.", "validationError": "Validation Error", "fillAllFields": "Please fill in all required fields", "configNotFound": "Configuration not found", "success": "Success", "configSaved": "Platform configurations saved successfully", "configDeleted": "Configuration deleted successfully", "configUpdated": "Configuration updated successfully", "error": "Error", "saveFailed": "Failed to save configurations", "deleteFailed": "Failed to delete configuration", "updateFailed": "Failed to update configuration", "loadFailed": "Failed to load platform configurations"}}